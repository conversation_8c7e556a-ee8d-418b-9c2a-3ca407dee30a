import { NextResponse } from 'next/server';
import fs from 'fs';
import ini from 'ini';

const TOKEN_FILE_PATH = './SAP-Service-Layer-Authorization-Token.ini';

interface SapTokenConfig {
  b1session?: string;
  routeid?: string;
  GeneratedAt?: number;
}

export async function GET(request: Request) {
  try {
    // Check if SAP credentials are configured
    const sapBaseUrl = process.env.SAP_BASE_URL;
    const sapCompanyDB = process.env.SAP_COMPANY_DB;
    const sapUsername = process.env.SAP_USERNAME;
    const sapPassword = process.env.SAP_PASSWORD;

    if (!sapBaseUrl || !sapCompanyDB || !sapUsername || !sapPassword) {
      return NextResponse.json({
        status: 'disconnected',
        expirationTime: null,
        error: 'SAP credentials not configured',
        tokenInfo: null
      });
    }

    // Check if token file exists and read token information
    let tokenInfo: SapTokenConfig | null = null;
    let tokenStatus = 'no-token';
    let calculatedExpiration: number | null = null;

    if (fs.existsSync(TOKEN_FILE_PATH)) {
      try {
        const fileContent = fs.readFileSync(TOKEN_FILE_PATH, 'utf-8');
        tokenInfo = ini.parse(fileContent);

        if (tokenInfo.GeneratedAt && tokenInfo.b1session && tokenInfo.routeid) {
          // Calculate expiration (SAP tokens typically last 30 minutes)
          const sessionTimeout = 30 * 60 * 1000; // 30 minutes in milliseconds
          calculatedExpiration = tokenInfo.GeneratedAt + sessionTimeout;
          const currentTime = Date.now();

          if (currentTime < calculatedExpiration) {
            tokenStatus = 'valid';
          } else {
            tokenStatus = 'expired';
          }
        }
      } catch (error) {
        console.error('Error reading token file:', error);
        tokenStatus = 'error';
      }
    }

    // Test actual connection to SAP Service Layer
    let connectionStatus = 'disconnected';
    try {
      const loginUrl = `${sapBaseUrl}/Login`;
      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          CompanyDB: sapCompanyDB,
          UserName: sapUsername,
          Password: sapPassword
        })
      });

      if (response.ok) {
        connectionStatus = 'connected';
        // If we don't have a valid token but connection works, use current time + 30 min
        if (tokenStatus !== 'valid') {
          calculatedExpiration = Date.now() + 30 * 60 * 1000;
        }
      }
    } catch (connectionError: any) {
      console.error('Connection test failed:', connectionError);
    }

    return NextResponse.json({
      status: connectionStatus,
      expirationTime: calculatedExpiration,
      tokenStatus,
      tokenInfo: tokenInfo ? {
        hasB1Session: !!tokenInfo.b1session,
        hasRouteId: !!tokenInfo.routeid,
        generatedAt: tokenInfo.GeneratedAt,
        b1sessionPreview: tokenInfo.b1session ? `${tokenInfo.b1session.substring(0, 20)}...` : null,
        routeidPreview: tokenInfo.routeid ? `${tokenInfo.routeid.substring(0, 20)}...` : null
      } : null,
      credentials: {
        baseUrl: sapBaseUrl,
        companyDB: sapCompanyDB,
        username: sapUsername
      }
    });

  } catch (error: any) {
    console.error('Error fetching SAP status:', error);
    return NextResponse.json({
      status: 'disconnected',
      expirationTime: null,
      error: 'Error fetching status',
      tokenInfo: null
    }, { status: 500 });
  }
}
